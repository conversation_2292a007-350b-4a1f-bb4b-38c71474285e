<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Customer;
use App\Models\Vendor;
use App\Models\BookedTable;
use App\Models\Order;
use App\Models\Notification;
use App\Models\Coupon;
use Carbon\Carbon;

class ImportFirebaseCollections extends Command
{
    protected $signature = 'firebase:import-collections {file=tests/collections.json}';
    protected $description = 'Import Firebase collections with progress tracking';

    public function handle()
    {
        $filePath = $this->argument('file');

        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("بدء استيراد بيانات Firebase...");
        $this->info("الملف: {$filePath}");

        // Read JSON file
        $jsonContent = file_get_contents($filePath);
        $data = json_decode($jsonContent, true);

        if (!$data || !isset($data['__collections__'])) {
            $this->error('Invalid JSON structure. Expected __collections__ key.');
            return 1;
        }

        $collections = $data['__collections__'];
        $this->info("تم العثور على " . count($collections) . " مجموعة");
        $this->line('');

        // Import each collection
        foreach ($collections as $collectionName => $collectionData) {
            $this->info("📁 معالجة المجموعة: {$collectionName}");
            $this->info("   عدد السجلات: " . count($collectionData));

            switch ($collectionName) {
                case 'booked_table':
                    $this->importBookedTables($collectionData);
                    break;
                case 'orders':
                    $this->importOrders($collectionData);
                    break;
                case 'notifications':
                    $this->importNotifications($collectionData);
                    break;
                case 'coupons':
                    $this->importCoupons($collectionData);
                    break;
                case 'users':
                    $this->importUsers($collectionData);
                    break;
                case 'vendors':
                    $this->importVendorsCollection($collectionData);
                    break;
                default:
                    $this->warn("   ⚠️ مجموعة غير مدعومة: {$collectionName}");
                    break;
            }
            $this->line('');
        }

        $this->info("✅ تم الانتهاء من الاستيراد بنجاح!");
        return 0;
    }

    private function importBookedTables($bookedTables)
    {
        $this->info("   📋 استيراد حجوزات الطاولات...");
        $imported = 0;

        foreach ($bookedTables as $id => $booking) {
            try {
                // Import customer if exists
                if (isset($booking['author'])) {
                    $this->importCustomer($booking['author']);
                }

                // Import vendor if exists
                if (isset($booking['vendor'])) {
                    $this->importVendor($booking['vendor']);
                }

                // Import booking
                BookedTable::updateOrCreate(
                    ['id' => $id],
                    [
                        'date' => $this->convertFirebaseTimestamp($booking['date'] ?? null),
                        'occasion' => $booking['occasion'] ?? null,
                        'guestLastName' => $booking['guestLastName'] ?? null,
                        'guestFirstName' => $booking['guestFirstName'] ?? null,
                        'guestEmail' => $booking['guestEmail'] ?? null,
                        'guestPhone' => $booking['guestPhone'] ?? null,
                        'discount' => $booking['discount'] ?? '0',
                        'vendorID' => $booking['vendorID'] ?? null,
                        'authorID' => $booking['authorID'] ?? null,
                        'totalGuest' => $booking['totalGuest'] ?? null,
                        'specialRequest' => $booking['specialRequest'] ?? null,
                        'discountType' => $booking['discountType'] ?? null,
                        'firstVisit' => $booking['firstVisit'] ?? false,
                        'status' => $booking['status'] ?? null,
                        'createdAt' => $this->convertFirebaseTimestamp($booking['createdAt'] ?? null),
                    ]
                );
                $imported++;
            } catch (\Exception $e) {
                $this->error("   خطأ في استيراد الحجز {$id}: " . $e->getMessage());
            }
        }

        $this->info("   ✅ تم استيراد {$imported} حجز");
    }

    private function importCustomer($customerData)
    {
        if (!isset($customerData['id'])) {
            return;
        }

        Customer::updateOrCreate(
            ['id' => $customerData['id']],
            [
                'firstName' => $customerData['firstName'] ?? null,
                'lastName' => $customerData['lastName'] ?? null,
                'email' => $customerData['email'] ?? null,
                'phoneNumber' => $customerData['phoneNumber'] ?? null,
                'countryCode' => $customerData['countryCode'] ?? null,
                'profilePictureURL' => $customerData['profilePictureURL'] ?? null,
                'role' => $customerData['role'] ?? 'customer',
                'active' => $customerData['active'] ?? true,
                'isDocumentVerify' => $customerData['isDocumentVerify'] ?? false,
                'isActive' => $customerData['isActive'] ?? null,
                'provider' => $customerData['provider'] ?? null,
                'appIdentifier' => $customerData['appIdentifier'] ?? null,
                'zoneId' => $customerData['zoneId'] ?? null,
                'fcmToken' => $customerData['fcmToken'] ?? null,
                'wallet_amount' => $customerData['wallet_amount'] ?? 0,
                'shippingAddress' => $customerData['shippingAddress'] ?? null,
                'createdAt' => $this->convertFirebaseTimestamp($customerData['createdAt'] ?? null),
            ]
        );
    }

    private function importVendor($vendorData)
    {
        if (!isset($vendorData['id'])) {
            return;
        }

        // Extract geohash from g object if exists
        $geohash = null;
        if (isset($vendorData['g']['geohash'])) {
            $geohash = $vendorData['g']['geohash'];
        }

        Vendor::updateOrCreate(
            ['id' => $vendorData['id']],
            [
                'title' => $vendorData['title'] ?? null,
                'description' => $vendorData['description'] ?? null,
                'latitude' => $vendorData['latitude'] ?? null,
                'longitude' => $vendorData['longitude'] ?? null,
                'phonenumber' => $vendorData['phonenumber'] ?? null,
                'reviewsCount' => $vendorData['reviewsCount'] ?? 0,
                'categoryPhoto' => $vendorData['categoryPhoto'] ?? null,
                'enabledDiveInFuture' => $vendorData['enabledDiveInFuture'] ?? true,
                'photos' => is_array($vendorData['photos'] ?? null) ? json_encode($vendorData['photos']) : ($vendorData['photos'] ?? null),
                'restaurantCost' => $vendorData['restaurantCost'] ?? null,
                'zoneId' => $vendorData['zoneId'] ?? null,
                'fcmToken' => $vendorData['fcmToken'] ?? null,
                'workingHours' => is_array($vendorData['workingHours'] ?? null) ? json_encode($vendorData['workingHours']) : ($vendorData['workingHours'] ?? null),
                'categoryID' => $vendorData['categoryID'] ?? null,
                'DeliveryCharge' => $vendorData['DeliveryCharge'] ?? null,
                'restaurantMenuPhotos' => is_array($vendorData['restaurantMenuPhotos'] ?? null) ? json_encode($vendorData['restaurantMenuPhotos']) : ($vendorData['restaurantMenuPhotos'] ?? null),
                'author' => is_array($vendorData['author'] ?? null) ? json_encode($vendorData['author']) : ($vendorData['author'] ?? null),
                'geohash' => $geohash,
                'categoryTitle' => $vendorData['categoryTitle'] ?? null,
                'coordinates' => is_array($vendorData['coordinates'] ?? null) ? json_encode($vendorData['coordinates']) : ($vendorData['coordinates'] ?? null),
                'photo' => $vendorData['photo'] ?? null,
                'filters' => is_array($vendorData['filters'] ?? null) ? json_encode($vendorData['filters']) : ($vendorData['filters'] ?? null),
                'closeDineTime' => $vendorData['closeDineTime'] ?? null,
                'walletAmount' => $vendorData['walletAmount'] ?? null,
                'authorProfilePic' => $vendorData['authorProfilePic'] ?? null,
                'openDineTime' => $vendorData['openDineTime'] ?? null,
                'authorName' => $vendorData['authorName'] ?? null,
                'reviewsSum' => $vendorData['reviewsSum'] ?? 0,
                'location' => $vendorData['location'] ?? null,
                'specialDiscount' => $vendorData['specialDiscount'] ?? null,
                'hidephotos' => $vendorData['hidephotos'] ?? null,
                'reststatus' => $vendorData['reststatus'] ?? null,
                'dine_in_active' => $vendorData['dine_in_active'] ?? null,
                'specialDiscountEnable' => $vendorData['specialDiscountEnable'] ?? false,
                'createdAt' => $this->convertFirebaseTimestamp($vendorData['createdAt'] ?? null),
            ]
        );
    }

    private function convertFirebaseTimestamp($timestamp)
    {
        if (!$timestamp || !isset($timestamp['__datatype__']) || $timestamp['__datatype__'] !== 'timestamp') {
            return null;
        }

        if (!isset($timestamp['value']['_seconds'])) {
            return null;
        }

        return Carbon::createFromTimestamp($timestamp['value']['_seconds']);
    }

    private function importOrders($orders)
    {
        $this->info("   📦 استيراد الطلبات...");
        $imported = 0;

        foreach ($orders as $id => $order) {
            try {
                Order::updateOrCreate(
                    ['id' => $id],
                    [
                        'authorID' => $order['authorID'] ?? null,
                        'vendorID' => $order['vendorID'] ?? null,
                        'driverID' => $order['driverID'] ?? null,
                        'status' => $order['status'] ?? 'Order Placed',
                        'products' => $order['products'] ?? null,
                        'subTotal' => $order['subTotal'] ?? 0,
                        'discount' => $order['discount'] ?? 0,
                        'discountType' => $order['discountType'] ?? null,
                        'couponCode' => $order['couponCode'] ?? null,
                        'deliveryCharge' => $order['deliveryCharge'] ?? 0,
                        'tipValue' => $order['tipValue'] ?? 0,
                        'tax' => $order['tax'] ?? 0,
                        'taxSetting' => $order['taxSetting'] ?? null,
                        'total' => $order['total'] ?? 0,
                        'address' => $order['address'] ?? null,
                        'author' => $order['author'] ?? null,
                        'vendor' => $order['vendor'] ?? null,
                        'driver' => $order['driver'] ?? null,
                        'paymentMethod' => $order['paymentMethod'] ?? null,
                        'paymentStatus' => $order['paymentStatus'] ?? false,
                        'orderType' => $order['orderType'] ?? 'delivery',
                        'notes' => $order['notes'] ?? null,
                        'estimatedTimeToPrepare' => $this->convertFirebaseTimestamp($order['estimatedTimeToPrepare'] ?? null),
                        'createdAt' => $this->convertFirebaseTimestamp($order['createdAt'] ?? null),
                    ]
                );
                $imported++;
            } catch (\Exception $e) {
                $this->error("   خطأ في استيراد الطلب {$id}: " . $e->getMessage());
            }
        }

        $this->info("   ✅ تم استيراد {$imported} طلب");
    }

    private function importNotifications($notifications)
    {
        $this->info("   🔔 استيراد الإشعارات...");
        $imported = 0;

        foreach ($notifications as $id => $notification) {
            try {
                Notification::updateOrCreate(
                    ['id' => $id],
                    [
                        'type' => $notification['type'] ?? 'general',
                        'title' => $notification['title'] ?? null,
                        'message' => $notification['message'] ?? null,
                        'recipientType' => $notification['recipientType'] ?? 'all',
                        'recipientId' => $notification['recipientId'] ?? null,
                        'data' => $notification['data'] ?? null,
                        'sent' => $notification['sent'] ?? false,
                        'sentAt' => $this->convertFirebaseTimestamp($notification['sentAt'] ?? null),
                        'createdAt' => $this->convertFirebaseTimestamp($notification['createdAt'] ?? null),
                    ]
                );
                $imported++;
            } catch (\Exception $e) {
                $this->error("   خطأ في استيراد الإشعار {$id}: " . $e->getMessage());
            }
        }

        $this->info("   ✅ تم استيراد {$imported} إشعار");
    }

    private function importCoupons($coupons)
    {
        $this->info("   🎫 استيراد الكوبونات...");
        $imported = 0;

        foreach ($coupons as $id => $coupon) {
            try {
                Coupon::updateOrCreate(
                    ['id' => $id],
                    [
                        'code' => $coupon['code'] ?? null,
                        'title' => $coupon['title'] ?? null,
                        'description' => $coupon['description'] ?? null,
                        'image' => $coupon['image'] ?? null,
                        'discountType' => $coupon['discountType'] ?? 'percentage',
                        'discount' => $coupon['discount'] ?? 0,
                        'minimumAmount' => $coupon['minimumAmount'] ?? 0,
                        'maximumAmount' => $coupon['maximumAmount'] ?? null,
                        'enabled' => $coupon['enabled'] ?? true,
                        'expiresAt' => $this->convertFirebaseTimestamp($coupon['expiresAt'] ?? null),
                        'vendorID' => $coupon['vendorID'] ?? null,
                        'usageLimit' => $coupon['usageLimit'] ?? null,
                        'usageCount' => $coupon['usageCount'] ?? 0,
                        'createdAt' => $this->convertFirebaseTimestamp($coupon['createdAt'] ?? null),
                    ]
                );
                $imported++;
            } catch (\Exception $e) {
                $this->error("   خطأ في استيراد الكوبون {$id}: " . $e->getMessage());
            }
        }

        $this->info("   ✅ تم استيراد {$imported} كوبون");
    }

    private function importUsers($users)
    {
        $this->info("   👥 استيراد المستخدمين...");
        $imported = 0;

        foreach ($users as $id => $user) {
            try {
                // Determine if this is a customer or vendor based on role
                if (isset($user['role']) && $user['role'] === 'vendor') {
                    $this->importVendor($user);
                } else {
                    $this->importCustomer($user);
                }
                $imported++;
            } catch (\Exception $e) {
                $this->error("   خطأ في استيراد المستخدم {$id}: " . $e->getMessage());
            }
        }

        $this->info("   ✅ تم استيراد {$imported} مستخدم");
    }

    private function importVendorsCollection($vendors)
    {
        $this->info("   🏪 استيراد المطاعم...");
        $imported = 0;

        foreach ($vendors as $id => $vendor) {
            try {
                $this->importVendor($vendor);
                $imported++;
            } catch (\Exception $e) {
                $this->error("   خطأ في استيراد المطعم {$id}: " . $e->getMessage());
            }
        }

        $this->info("   ✅ تم استيراد {$imported} مطعم");
    }
}
