<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Customer;
use App\Models\Vendor;
use App\Models\BookedTable;
use Carbon\Carbon;

class ImportFirebaseData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'firebase:import {file=tests/collections.json}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Firebase/Firestore collections data into PostgreSQL';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');

        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("Starting import from {$filePath}...");

        $jsonContent = file_get_contents($filePath);
        $data = json_decode($jsonContent, true);

        if (!$data || !isset($data['__collections__'])) {
            $this->error('Invalid JSON structure. Expected __collections__ key.');
            return 1;
        }

        $collections = $data['__collections__'];

        if (isset($collections['booked_table'])) {
            $this->importBookedTables($collections['booked_table']);
        }

        $this->info('Import completed successfully!');
        return 0;
    }

    private function importBookedTables($bookedTables)
    {
        $this->info('Importing booked tables...');
        $bar = $this->output->createProgressBar(count($bookedTables));
        $bar->start();

        foreach ($bookedTables as $id => $booking) {
            // Import customer if not exists
            if (isset($booking['author'])) {
                $this->importCustomer($booking['author']);
            }

            // Import vendor if not exists
            if (isset($booking['vendor'])) {
                $this->importVendor($booking['vendor']);
            }

            // Import booking
            $this->importBooking($id, $booking);

            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Booked tables imported successfully!');
    }

    private function importCustomer($customerData)
    {
        if (!isset($customerData['id'])) {
            return;
        }

        Customer::updateOrCreate(
            ['id' => $customerData['id']],
            [
                'firstName' => $customerData['firstName'] ?? null,
                'lastName' => $customerData['lastName'] ?? null,
                'email' => $customerData['email'] ?? null,
                'phoneNumber' => $customerData['phoneNumber'] ?? null,
                'countryCode' => $customerData['countryCode'] ?? null,
                'profilePictureURL' => $customerData['profilePictureURL'] ?? null,
                'role' => $customerData['role'] ?? 'customer',
                'active' => $customerData['active'] ?? true,
                'isDocumentVerify' => $customerData['isDocumentVerify'] ?? false,
                'isActive' => $customerData['isActive'] ?? null,
                'provider' => $customerData['provider'] ?? null,
                'appIdentifier' => $customerData['appIdentifier'] ?? null,
                'zoneId' => $customerData['zoneId'] ?? null,
                'fcmToken' => $customerData['fcmToken'] ?? null,
                'wallet_amount' => $customerData['wallet_amount'] ?? 0,
                'shippingAddress' => $customerData['shippingAddress'] ?? null,
                'createdAt' => $this->convertFirebaseTimestamp($customerData['createdAt'] ?? null),
            ]
        );
    }

    private function importVendor($vendorData)
    {
        if (!isset($vendorData['id'])) {
            return;
        }

        // Extract geohash from g object if exists
        $geohash = null;
        if (isset($vendorData['g']['geohash'])) {
            $geohash = $vendorData['g']['geohash'];
        }

        Vendor::updateOrCreate(
            ['id' => $vendorData['id']],
            [
                'title' => $vendorData['title'] ?? null,
                'description' => $vendorData['description'] ?? null,
                'latitude' => $vendorData['latitude'] ?? null,
                'longitude' => $vendorData['longitude'] ?? null,
                'phonenumber' => $vendorData['phonenumber'] ?? null,
                'reviewsCount' => $vendorData['reviewsCount'] ?? 0,
                'categoryPhoto' => $vendorData['categoryPhoto'] ?? null,
                'enabledDiveInFuture' => $vendorData['enabledDiveInFuture'] ?? true,
                'photos' => $vendorData['photos'] ?? null,
                'restaurantCost' => $vendorData['restaurantCost'] ?? null,
                'zoneId' => $vendorData['zoneId'] ?? null,
                'fcmToken' => $vendorData['fcmToken'] ?? null,
                'workingHours' => $vendorData['workingHours'] ?? null,
                'categoryID' => $vendorData['categoryID'] ?? null,
                'DeliveryCharge' => $vendorData['DeliveryCharge'] ?? null,
                'restaurantMenuPhotos' => $vendorData['restaurantMenuPhotos'] ?? null,
                'author' => $vendorData['author'] ?? null,
                'geohash' => $geohash,
                'categoryTitle' => $vendorData['categoryTitle'] ?? null,
                'coordinates' => $vendorData['coordinates'] ?? null,
                'photo' => $vendorData['photo'] ?? null,
                'filters' => $vendorData['filters'] ?? null,
                'closeDineTime' => $vendorData['closeDineTime'] ?? null,
                'walletAmount' => $vendorData['walletAmount'] ?? null,
                'authorProfilePic' => $vendorData['authorProfilePic'] ?? null,
                'openDineTime' => $vendorData['openDineTime'] ?? null,
                'authorName' => $vendorData['authorName'] ?? null,
                'reviewsSum' => $vendorData['reviewsSum'] ?? 0,
                'location' => $vendorData['location'] ?? null,
                'specialDiscount' => $vendorData['specialDiscount'] ?? null,
                'hidephotos' => $vendorData['hidephotos'] ?? null,
                'reststatus' => $vendorData['reststatus'] ?? null,
                'dine_in_active' => $vendorData['dine_in_active'] ?? null,
                'specialDiscountEnable' => $vendorData['specialDiscountEnable'] ?? false,
                'createdAt' => $this->convertFirebaseTimestamp($vendorData['createdAt'] ?? null),
            ]
        );
    }

    private function importBooking($id, $bookingData)
    {
        BookedTable::updateOrCreate(
            ['id' => $id],
            [
                'date' => $this->convertFirebaseTimestamp($bookingData['date'] ?? null),
                'occasion' => $bookingData['occasion'] ?? null,
                'guestLastName' => $bookingData['guestLastName'] ?? null,
                'guestFirstName' => $bookingData['guestFirstName'] ?? null,
                'guestEmail' => $bookingData['guestEmail'] ?? null,
                'guestPhone' => $bookingData['guestPhone'] ?? null,
                'discount' => $bookingData['discount'] ?? '0',
                'vendorID' => $bookingData['vendorID'] ?? null,
                'authorID' => $bookingData['authorID'] ?? null,
                'totalGuest' => $bookingData['totalGuest'] ?? null,
                'specialRequest' => $bookingData['specialRequest'] ?? null,
                'discountType' => $bookingData['discountType'] ?? null,
                'firstVisit' => $bookingData['firstVisit'] ?? false,
                'status' => $bookingData['status'] ?? null,
                'createdAt' => $this->convertFirebaseTimestamp($bookingData['createdAt'] ?? null),
            ]
        );
    }

    private function convertFirebaseTimestamp($timestamp)
    {
        if (!$timestamp || !isset($timestamp['__datatype__']) || $timestamp['__datatype__'] !== 'timestamp') {
            return null;
        }

        if (!isset($timestamp['value']['_seconds'])) {
            return null;
        }

        return Carbon::createFromTimestamp($timestamp['value']['_seconds']);
    }
}
