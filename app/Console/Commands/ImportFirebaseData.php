<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Customer;
use App\Models\Vendor;
use App\Models\BookedTable;
use Carbon\Carbon;

class ImportFirebaseData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'firebase:import {file=tests/collections.json}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Firebase/Firestore collections data into PostgreSQL';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');

        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("Starting import from {$filePath}...");

        $jsonContent = file_get_contents($filePath);
        $data = json_decode($jsonContent, true);

        if (!$data || !isset($data['__collections__'])) {
            $this->error('Invalid JSON structure. Expected __collections__ key.');
            return 1;
        }

        $collections = $data['__collections__'];

        // Import available collections
        if (isset($collections['booked_table'])) {
            $this->importBookedTables($collections['booked_table']);
        }

        if (isset($collections['categories'])) {
            $this->importCategories($collections['categories']);
        }

        if (isset($collections['foods'])) {
            $this->importFoods($collections['foods']);
        }

        if (isset($collections['orders'])) {
            $this->importOrders($collections['orders']);
        }

        if (isset($collections['reviews'])) {
            $this->importReviews($collections['reviews']);
        }

        if (isset($collections['coupons'])) {
            $this->importCoupons($collections['coupons']);
        }

        if (isset($collections['drivers'])) {
            $this->importDrivers($collections['drivers']);
        }

        if (isset($collections['notifications'])) {
            $this->importNotifications($collections['notifications']);
        }

        $this->info('Import completed successfully!');
        return 0;
    }

    private function importBookedTables($bookedTables)
    {
        $this->info('Importing booked tables...');
        $bar = $this->output->createProgressBar(count($bookedTables));
        $bar->start();

        foreach ($bookedTables as $id => $booking) {
            // Import customer if not exists
            if (isset($booking['author'])) {
                $this->importCustomer($booking['author']);
            }

            // Import vendor if not exists
            if (isset($booking['vendor'])) {
                $this->importVendor($booking['vendor']);
            }

            // Import booking
            $this->importBooking($id, $booking);

            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Booked tables imported successfully!');
    }

    private function importCustomer($customerData)
    {
        if (!isset($customerData['id'])) {
            return;
        }

        Customer::updateOrCreate(
            ['id' => $customerData['id']],
            [
                'firstName' => $customerData['firstName'] ?? null,
                'lastName' => $customerData['lastName'] ?? null,
                'email' => $customerData['email'] ?? null,
                'phoneNumber' => $customerData['phoneNumber'] ?? null,
                'countryCode' => $customerData['countryCode'] ?? null,
                'profilePictureURL' => $customerData['profilePictureURL'] ?? null,
                'role' => $customerData['role'] ?? 'customer',
                'active' => $customerData['active'] ?? true,
                'isDocumentVerify' => $customerData['isDocumentVerify'] ?? false,
                'isActive' => $customerData['isActive'] ?? null,
                'provider' => $customerData['provider'] ?? null,
                'appIdentifier' => $customerData['appIdentifier'] ?? null,
                'zoneId' => $customerData['zoneId'] ?? null,
                'fcmToken' => $customerData['fcmToken'] ?? null,
                'wallet_amount' => $customerData['wallet_amount'] ?? 0,
                'shippingAddress' => $customerData['shippingAddress'] ?? null,
                'createdAt' => $this->convertFirebaseTimestamp($customerData['createdAt'] ?? null),
            ]
        );
    }

    private function importVendor($vendorData)
    {
        if (!isset($vendorData['id'])) {
            return;
        }

        // Extract geohash from g object if exists
        $geohash = null;
        if (isset($vendorData['g']['geohash'])) {
            $geohash = $vendorData['g']['geohash'];
        }

        Vendor::updateOrCreate(
            ['id' => $vendorData['id']],
            [
                'title' => $vendorData['title'] ?? null,
                'description' => $vendorData['description'] ?? null,
                'latitude' => $vendorData['latitude'] ?? null,
                'longitude' => $vendorData['longitude'] ?? null,
                'phonenumber' => $vendorData['phonenumber'] ?? null,
                'reviewsCount' => $vendorData['reviewsCount'] ?? 0,
                'categoryPhoto' => $vendorData['categoryPhoto'] ?? null,
                'enabledDiveInFuture' => $vendorData['enabledDiveInFuture'] ?? true,
                'photos' => $vendorData['photos'] ?? null,
                'restaurantCost' => $vendorData['restaurantCost'] ?? null,
                'zoneId' => $vendorData['zoneId'] ?? null,
                'fcmToken' => $vendorData['fcmToken'] ?? null,
                'workingHours' => $vendorData['workingHours'] ?? null,
                'categoryID' => $vendorData['categoryID'] ?? null,
                'DeliveryCharge' => $vendorData['DeliveryCharge'] ?? null,
                'restaurantMenuPhotos' => $vendorData['restaurantMenuPhotos'] ?? null,
                'author' => $vendorData['author'] ?? null,
                'geohash' => $geohash,
                'categoryTitle' => $vendorData['categoryTitle'] ?? null,
                'coordinates' => $vendorData['coordinates'] ?? null,
                'photo' => $vendorData['photo'] ?? null,
                'filters' => $vendorData['filters'] ?? null,
                'closeDineTime' => $vendorData['closeDineTime'] ?? null,
                'walletAmount' => $vendorData['walletAmount'] ?? null,
                'authorProfilePic' => $vendorData['authorProfilePic'] ?? null,
                'openDineTime' => $vendorData['openDineTime'] ?? null,
                'authorName' => $vendorData['authorName'] ?? null,
                'reviewsSum' => $vendorData['reviewsSum'] ?? 0,
                'location' => $vendorData['location'] ?? null,
                'specialDiscount' => $vendorData['specialDiscount'] ?? null,
                'hidephotos' => $vendorData['hidephotos'] ?? null,
                'reststatus' => $vendorData['reststatus'] ?? null,
                'dine_in_active' => $vendorData['dine_in_active'] ?? null,
                'specialDiscountEnable' => $vendorData['specialDiscountEnable'] ?? false,
                'createdAt' => $this->convertFirebaseTimestamp($vendorData['createdAt'] ?? null),
            ]
        );
    }

    private function importBooking($id, $bookingData)
    {
        BookedTable::updateOrCreate(
            ['id' => $id],
            [
                'date' => $this->convertFirebaseTimestamp($bookingData['date'] ?? null),
                'occasion' => $bookingData['occasion'] ?? null,
                'guestLastName' => $bookingData['guestLastName'] ?? null,
                'guestFirstName' => $bookingData['guestFirstName'] ?? null,
                'guestEmail' => $bookingData['guestEmail'] ?? null,
                'guestPhone' => $bookingData['guestPhone'] ?? null,
                'discount' => $bookingData['discount'] ?? '0',
                'vendorID' => $bookingData['vendorID'] ?? null,
                'authorID' => $bookingData['authorID'] ?? null,
                'totalGuest' => $bookingData['totalGuest'] ?? null,
                'specialRequest' => $bookingData['specialRequest'] ?? null,
                'discountType' => $bookingData['discountType'] ?? null,
                'firstVisit' => $bookingData['firstVisit'] ?? false,
                'status' => $bookingData['status'] ?? null,
                'createdAt' => $this->convertFirebaseTimestamp($bookingData['createdAt'] ?? null),
            ]
        );
    }

    private function convertFirebaseTimestamp($timestamp)
    {
        if (!$timestamp || !isset($timestamp['__datatype__']) || $timestamp['__datatype__'] !== 'timestamp') {
            return null;
        }

        if (!isset($timestamp['value']['_seconds'])) {
            return null;
        }

        return Carbon::createFromTimestamp($timestamp['value']['_seconds']);
    }

    private function importCategories($categories)
    {
        $this->info('Importing categories...');
        $bar = $this->output->createProgressBar(count($categories));
        $bar->start();

        foreach ($categories as $id => $category) {
            \App\Models\Category::updateOrCreate(
                ['id' => $id],
                [
                    'title' => $category['title'] ?? null,
                    'description' => $category['description'] ?? null,
                    'image' => $category['image'] ?? null,
                    'publish' => $category['publish'] ?? true,
                    'order' => $category['order'] ?? 0,
                    'filters' => $category['filters'] ?? null,
                    'createdAt' => $this->convertFirebaseTimestamp($category['createdAt'] ?? null),
                ]
            );
            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Categories imported successfully!');
    }

    private function importFoods($foods)
    {
        $this->info('Importing foods...');
        $bar = $this->output->createProgressBar(count($foods));
        $bar->start();

        foreach ($foods as $id => $food) {
            \App\Models\Food::updateOrCreate(
                ['id' => $id],
                [
                    'name' => $food['name'] ?? null,
                    'description' => $food['description'] ?? null,
                    'price' => $food['price'] ?? 0,
                    'disPrice' => $food['disPrice'] ?? null,
                    'photo' => $food['photo'] ?? null,
                    'photos' => $food['photos'] ?? null,
                    'categoryID' => $food['categoryID'] ?? null,
                    'vendorID' => $food['vendorID'] ?? null,
                    'publish' => $food['publish'] ?? true,
                    'nonveg' => $food['nonveg'] ?? false,
                    'addOnsTitle' => $food['addOnsTitle'] ?? null,
                    'addOns' => $food['addOns'] ?? null,
                    'size' => $food['size'] ?? null,
                    'calories' => $food['calories'] ?? null,
                    'ingredients' => $food['ingredients'] ?? null,
                    'nutritions' => $food['nutritions'] ?? null,
                    'takeawayOption' => $food['takeawayOption'] ?? true,
                    'createdAt' => $this->convertFirebaseTimestamp($food['createdAt'] ?? null),
                ]
            );
            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Foods imported successfully!');
    }

    private function importOrders($orders)
    {
        $this->info('Importing orders...');
        $bar = $this->output->createProgressBar(count($orders));
        $bar->start();

        foreach ($orders as $id => $order) {
            \App\Models\Order::updateOrCreate(
                ['id' => $id],
                [
                    'authorID' => $order['authorID'] ?? null,
                    'vendorID' => $order['vendorID'] ?? null,
                    'driverID' => $order['driverID'] ?? null,
                    'status' => $order['status'] ?? 'Order Placed',
                    'products' => $order['products'] ?? null,
                    'subTotal' => $order['subTotal'] ?? 0,
                    'discount' => $order['discount'] ?? 0,
                    'discountType' => $order['discountType'] ?? null,
                    'couponCode' => $order['couponCode'] ?? null,
                    'deliveryCharge' => $order['deliveryCharge'] ?? 0,
                    'tipValue' => $order['tipValue'] ?? 0,
                    'tax' => $order['tax'] ?? 0,
                    'taxSetting' => $order['taxSetting'] ?? null,
                    'total' => $order['total'] ?? 0,
                    'address' => $order['address'] ?? null,
                    'author' => $order['author'] ?? null,
                    'vendor' => $order['vendor'] ?? null,
                    'driver' => $order['driver'] ?? null,
                    'paymentMethod' => $order['paymentMethod'] ?? null,
                    'paymentStatus' => $order['paymentStatus'] ?? false,
                    'orderType' => $order['orderType'] ?? 'delivery',
                    'notes' => $order['notes'] ?? null,
                    'estimatedTimeToPrepare' => $this->convertFirebaseTimestamp($order['estimatedTimeToPrepare'] ?? null),
                    'createdAt' => $this->convertFirebaseTimestamp($order['createdAt'] ?? null),
                ]
            );
            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Orders imported successfully!');
    }

    private function importReviews($reviews)
    {
        $this->info('Importing reviews...');
        $bar = $this->output->createProgressBar(count($reviews));
        $bar->start();

        foreach ($reviews as $id => $review) {
            \App\Models\Review::updateOrCreate(
                ['id' => $id],
                [
                    'vendorID' => $review['vendorID'] ?? null,
                    'authorID' => $review['authorID'] ?? null,
                    'orderID' => $review['orderID'] ?? null,
                    'rating' => $review['rating'] ?? 5,
                    'comment' => $review['comment'] ?? null,
                    'photos' => $review['photos'] ?? null,
                    'isApproved' => $review['isApproved'] ?? true,
                    'createdAt' => $this->convertFirebaseTimestamp($review['createdAt'] ?? null),
                ]
            );
            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Reviews imported successfully!');
    }

    private function importCoupons($coupons)
    {
        $this->info('Importing coupons...');
        $bar = $this->output->createProgressBar(count($coupons));
        $bar->start();

        foreach ($coupons as $id => $coupon) {
            \App\Models\Coupon::updateOrCreate(
                ['id' => $id],
                [
                    'code' => $coupon['code'] ?? null,
                    'title' => $coupon['title'] ?? null,
                    'description' => $coupon['description'] ?? null,
                    'image' => $coupon['image'] ?? null,
                    'discountType' => $coupon['discountType'] ?? 'percentage',
                    'discount' => $coupon['discount'] ?? 0,
                    'minimumAmount' => $coupon['minimumAmount'] ?? 0,
                    'maximumAmount' => $coupon['maximumAmount'] ?? null,
                    'enabled' => $coupon['enabled'] ?? true,
                    'expiresAt' => $this->convertFirebaseTimestamp($coupon['expiresAt'] ?? null),
                    'vendorID' => $coupon['vendorID'] ?? null,
                    'usageLimit' => $coupon['usageLimit'] ?? null,
                    'usageCount' => $coupon['usageCount'] ?? 0,
                    'createdAt' => $this->convertFirebaseTimestamp($coupon['createdAt'] ?? null),
                ]
            );
            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Coupons imported successfully!');
    }

    private function importDrivers($drivers)
    {
        $this->info('Importing drivers...');
        $bar = $this->output->createProgressBar(count($drivers));
        $bar->start();

        foreach ($drivers as $id => $driver) {
            \App\Models\Driver::updateOrCreate(
                ['id' => $id],
                [
                    'firstName' => $driver['firstName'] ?? null,
                    'lastName' => $driver['lastName'] ?? null,
                    'email' => $driver['email'] ?? null,
                    'phoneNumber' => $driver['phoneNumber'] ?? null,
                    'countryCode' => $driver['countryCode'] ?? null,
                    'profilePictureURL' => $driver['profilePictureURL'] ?? null,
                    'role' => $driver['role'] ?? 'driver',
                    'active' => $driver['active'] ?? true,
                    'isDocumentVerify' => $driver['isDocumentVerify'] ?? false,
                    'vehicleType' => $driver['vehicleType'] ?? null,
                    'vehicleNumber' => $driver['vehicleNumber'] ?? null,
                    'carName' => $driver['carName'] ?? null,
                    'carNumber' => $driver['carNumber'] ?? null,
                    'carPictureURL' => $driver['carPictureURL'] ?? null,
                    'latitude' => $driver['latitude'] ?? null,
                    'longitude' => $driver['longitude'] ?? null,
                    'isOnline' => $driver['isOnline'] ?? false,
                    'wallet_amount' => $driver['wallet_amount'] ?? 0,
                    'zoneId' => $driver['zoneId'] ?? null,
                    'fcmToken' => $driver['fcmToken'] ?? null,
                    'createdAt' => $this->convertFirebaseTimestamp($driver['createdAt'] ?? null),
                ]
            );
            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Drivers imported successfully!');
    }

    private function importNotifications($notifications)
    {
        $this->info('Importing notifications...');
        $bar = $this->output->createProgressBar(count($notifications));
        $bar->start();

        foreach ($notifications as $id => $notification) {
            \App\Models\Notification::updateOrCreate(
                ['id' => $id],
                [
                    'type' => $notification['type'] ?? 'general',
                    'title' => $notification['title'] ?? null,
                    'message' => $notification['message'] ?? null,
                    'recipientType' => $notification['recipientType'] ?? 'all',
                    'recipientId' => $notification['recipientId'] ?? null,
                    'data' => $notification['data'] ?? null,
                    'sent' => $notification['sent'] ?? false,
                    'sentAt' => $this->convertFirebaseTimestamp($notification['sentAt'] ?? null),
                    'createdAt' => $this->convertFirebaseTimestamp($notification['createdAt'] ?? null),
                ]
            );
            $bar->advance();
        }

        $bar->finish();
        $this->line('');
        $this->info('Notifications imported successfully!');
    }
}
