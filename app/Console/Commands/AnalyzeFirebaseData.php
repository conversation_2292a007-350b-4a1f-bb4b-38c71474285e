<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class AnalyzeFirebaseData extends Command
{
    protected $signature = 'firebase:analyze {file=tests/collections.json}';
    protected $description = 'Analyze Firebase collections structure and content';

    public function handle()
    {
        $filePath = $this->argument('file');

        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("=== تحليل بيانات Firebase ===");
        $this->info("الملف: {$filePath}");
        $this->info("الحجم: " . round(filesize($filePath) / 1024 / 1024, 2) . " ميجابايت");
        $this->line('');

        // Read file in chunks to handle large files
        $handle = fopen($filePath, 'r');
        $chunk = fread($handle, 50000); // Read first 50KB
        fclose($handle);

        // Try to find collections structure
        if (strpos($chunk, '"__collections__"') !== false) {
            $this->info("✅ تم العثور على مفتاح __collections__");
            
            // Extract collection names
            preg_match_all('/"([^"]+)":{"([^"]+)"/', $chunk, $matches);
            
            if (!empty($matches[1])) {
                $this->info("المجموعات المكتشفة:");
                $collections = array_unique($matches[1]);
                foreach ($collections as $collection) {
                    if ($collection !== '__collections__' && strlen($collection) > 3) {
                        $this->info("  📁 {$collection}");
                    }
                }
            }
            
            // Try to count records for booked_table specifically
            $bookedTableMatches = substr_count($chunk, '"booked_table"');
            if ($bookedTableMatches > 0) {
                $this->info("  📊 booked_table: تم العثور على إشارات");
            }
            
        } else {
            $this->error("❌ لم يتم العثور على مفتاح __collections__");
        }

        return 0;
    }
}
