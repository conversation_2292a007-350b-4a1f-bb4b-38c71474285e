<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    protected $table = 'customers';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'firstName', 'lastName', 'email', 'phoneNumber', 'countryCode',
        'profilePictureURL', 'role', 'active', 'isDocumentVerify', 'isActive',
        'provider', 'appIdentifier', 'zoneId', 'fcmToken', 'wallet_amount',
        'shippingAddress', 'createdAt'
    ];

    protected $casts = [
        'active' => 'boolean',
        'isDocumentVerify' => 'boolean',
        'isActive' => 'boolean',
        'wallet_amount' => 'decimal:2',
        'shippingAddress' => 'array',
        'createdAt' => 'datetime'
    ];

    public function bookedTables()
    {
        return $this->hasMany(BookedTable::class, 'authorID');
    }
}
