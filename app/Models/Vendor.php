<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;

    protected $table = 'vendors';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'title', 'description', 'latitude', 'longitude', 'phonenumber',
        'reviewsCount', 'categoryPhoto', 'enabledDiveInFuture', 'photos',
        'restaurantCost', 'zoneId', 'fcmToken', 'workingHours', 'categoryID',
        'DeliveryCharge', 'restaurantMenuPhotos', 'author', 'geohash',
        'categoryTitle', 'coordinates', 'photo', 'filters', 'closeDineTime',
        'walletAmount', 'authorProfilePic', 'openDineTime', 'authorName',
        'reviewsSum', 'location', 'specialDiscount', 'hidephotos', 'reststatus',
        'dine_in_active', 'specialDiscountEnable', 'createdAt'
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'reviewsCount' => 'integer',
        'enabledDiveInFuture' => 'boolean',
        'photos' => 'array',
        'workingHours' => 'array',
        'DeliveryCharge' => 'array',
        'restaurantMenuPhotos' => 'array',
        'coordinates' => 'array',
        'filters' => 'array',
        'walletAmount' => 'decimal:2',
        'reviewsSum' => 'decimal:2',
        'specialDiscount' => 'array',
        'hidephotos' => 'boolean',
        'reststatus' => 'boolean',
        'dine_in_active' => 'boolean',
        'specialDiscountEnable' => 'boolean',
        'createdAt' => 'datetime'
    ];

    public function bookedTables()
    {
        return $this->hasMany(BookedTable::class, 'vendorID');
    }
}
