<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BookedTable extends Model
{
    use HasFactory;

    protected $table = 'booked_tables';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'date', 'occasion', 'guestLastName', 'guestFirstName',
        'guestEmail', 'guestPhone', 'discount', 'vendorID', 'authorID',
        'totalGuest', 'specialRequest', 'discountType', 'firstVisit',
        'status', 'createdAt'
    ];

    protected $casts = [
        'date' => 'datetime',
        'firstVisit' => 'boolean',
        'createdAt' => 'datetime'
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'authorID');
    }
}
