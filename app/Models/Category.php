<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $connection = 'firebase'; // Use PostgreSQL connection for Firebase data
    protected $table = 'categories';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'title', 'description', 'image', 'publish', 'order', 'filters', 'createdAt'
    ];

    protected $casts = [
        'publish' => 'boolean',
        'order' => 'integer',
        'filters' => 'array',
        'createdAt' => 'datetime'
    ];

    public function foods()
    {
        return $this->hasMany(Food::class, 'categoryID');
    }
}
