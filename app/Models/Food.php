<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Food extends Model
{
    use HasFactory;

    protected $connection = 'firebase'; // Use PostgreSQL connection for Firebase data
    protected $table = 'foods';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'name', 'description', 'price', 'disPrice', 'photo', 'photos',
        'categoryID', 'vendorID', 'publish', 'nonveg', 'addOnsTitle', 'addOns',
        'size', 'calories', 'ingredients', 'nutritions', 'takeawayOption', 'createdAt'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'disPrice' => 'decimal:2',
        'photos' => 'array',
        'publish' => 'boolean',
        'nonveg' => 'boolean',
        'addOnsTitle' => 'array',
        'addOns' => 'array',
        'size' => 'array',
        'calories' => 'integer',
        'nutritions' => 'array',
        'takeawayOption' => 'boolean',
        'createdAt' => 'datetime'
    ];

    public function category()
    {
        return $this->belongsTo(Category::class, 'categoryID');
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }
}
