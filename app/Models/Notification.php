<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $connection = 'firebase'; // Use PostgreSQL connection for Firebase data
    protected $table = 'notifications';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'type', 'title', 'message', 'recipientType', 'recipientId',
        'data', 'sent', 'sentAt', 'createdAt'
    ];

    protected $casts = [
        'data' => 'array',
        'sent' => 'boolean',
        'sentAt' => 'datetime',
        'createdAt' => 'datetime'
    ];
}
