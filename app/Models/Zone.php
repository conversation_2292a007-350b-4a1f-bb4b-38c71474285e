<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Zone extends Model
{
    use HasFactory;

    protected $connection = 'firebase'; // Use PostgreSQL connection for Firebase data
    protected $table = 'zones';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'name', 'description', 'coordinates', 'active', 'deliveryCharge'
    ];

    protected $casts = [
        'coordinates' => 'array',
        'active' => 'boolean',
        'deliveryCharge' => 'decimal:2'
    ];

    public function vendors()
    {
        return $this->hasMany(Vendor::class, 'zoneId');
    }

    public function drivers()
    {
        return $this->hasMany(Driver::class, 'zoneId');
    }

    public function customers()
    {
        return $this->hasMany(Customer::class, 'zoneId');
    }
}
