<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    protected $connection = 'mysql'; // Use MySQL for admin settings
    protected $table = 'settings';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'key', 'value', 'type', 'description', 'category', 'is_public'
    ];

    protected $casts = [
        'is_public' => 'boolean'
    ];

    public function getValueAttribute($value)
    {
        switch ($this->type) {
            case 'json':
                return json_decode($value, true);
            case 'boolean':
                return (bool) $value;
            case 'number':
                return is_numeric($value) ? (float) $value : $value;
            default:
                return $value;
        }
    }

    public function setValueAttribute($value)
    {
        if ($this->type === 'json') {
            $this->attributes['value'] = json_encode($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }
}
