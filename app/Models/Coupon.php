<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    use HasFactory;

    protected $connection = 'firebase'; // Use PostgreSQL connection for Firebase data
    protected $table = 'coupons';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'code', 'title', 'description', 'image', 'discountType', 'discount',
        'minimumAmount', 'maximumAmount', 'enabled', 'expiresAt', 'vendorID',
        'usageLimit', 'usageCount', 'createdAt'
    ];

    protected $casts = [
        'discount' => 'decimal:2',
        'minimumAmount' => 'decimal:2',
        'maximumAmount' => 'decimal:2',
        'enabled' => 'boolean',
        'usageLimit' => 'integer',
        'usageCount' => 'integer',
        'expiresAt' => 'datetime',
        'createdAt' => 'datetime'
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'couponCode', 'code');
    }
}
