<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $connection = 'mysql'; // Use MySQL for admin settings
    protected $table = 'payment_methods';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'name', 'type', 'description', 'image', 'isEnabled', 'settings',
        'supportedCurrencies', 'processingFee', 'fixedFee', 'order', 'isLive'
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isLive' => 'boolean',
        'settings' => 'array',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'fixedFee' => 'decimal:2',
        'order' => 'integer'
    ];

    public function paymentTransactions()
    {
        return $this->hasMany(PaymentTransaction::class, 'paymentMethodID');
    }

    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }
}
