<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    use HasFactory;

    protected $connection = 'firebase'; // Use PostgreSQL connection for Firebase data
    protected $table = 'reviews';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id', 'vendorID', 'authorID', 'orderID', 'rating', 'comment', 'photos', 'isApproved', 'createdAt'
    ];

    protected $casts = [
        'rating' => 'decimal:1',
        'photos' => 'array',
        'isApproved' => 'boolean',
        'createdAt' => 'datetime'
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'authorID');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'orderID');
    }
}
