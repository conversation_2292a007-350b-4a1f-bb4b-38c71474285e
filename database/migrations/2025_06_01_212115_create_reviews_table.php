<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('reviews', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('vendorID'); // Restaurant ID
            $table->string('authorID'); // Customer ID
            $table->string('orderID')->nullable(); // Order ID if review is for an order
            $table->decimal('rating', 2, 1); // Rating out of 5
            $table->text('comment')->nullable();
            $table->json('photos')->nullable(); // Review photos
            $table->boolean('isApproved')->default(true);
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['vendorID', 'isApproved']);
            $table->index(['authorID']);
            $table->index(['rating']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
