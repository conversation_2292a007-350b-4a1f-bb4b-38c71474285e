<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code', 3)->unique(); // USD, EUR, etc.
            $table->string('symbol'); // $, €, etc.
            $table->decimal('rate', 10, 4)->default(1); // Exchange rate
            $table->boolean('active')->default(true);
            $table->boolean('isDefault')->default(false);
            $table->timestamps();

            $table->index(['active', 'isDefault']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
