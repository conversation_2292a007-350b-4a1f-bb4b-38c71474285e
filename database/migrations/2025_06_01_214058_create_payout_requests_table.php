<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('payout_requests', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('userID'); // Vendor or Driver ID
            $table->string('userType'); // vendor, driver
            $table->string('withdrawMethodID'); // Withdrawal method
            $table->decimal('amount', 10, 2);
            $table->decimal('processingFee', 10, 2)->default(0);
            $table->decimal('netAmount', 10, 2); // Amount after fees
            $table->string('status')->default('pending'); // pending, approved, processing, completed, rejected
            $table->json('withdrawDetails'); // Bank details, PayPal email, etc.
            $table->text('notes')->nullable(); // Admin notes
            $table->text('rejectionReason')->nullable();
            $table->string('adminID')->nullable(); // Admin who processed
            $table->timestamp('processedAt')->nullable();
            $table->timestamp('completedAt')->nullable();
            $table->string('transactionReference')->nullable(); // External reference
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['userID', 'userType', 'status']);
            $table->index(['status', 'createdAt']);
            $table->index(['withdrawMethodID']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payout_requests');
    }
};
