<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('authorID'); // Customer ID
            $table->string('vendorID'); // Restaurant ID
            $table->string('driverID')->nullable(); // Driver ID
            $table->string('status')->default('Order Placed');
            $table->timestamp('createdAt')->nullable();
            $table->json('products'); // Order items
            $table->decimal('subTotal', 10, 2);
            $table->decimal('discount', 10, 2)->default(0);
            $table->string('discountType')->nullable();
            $table->string('couponCode')->nullable();
            $table->decimal('deliveryCharge', 10, 2)->default(0);
            $table->decimal('tipValue', 10, 2)->default(0);
            $table->decimal('tax', 10, 2)->default(0);
            $table->json('taxSetting')->nullable();
            $table->decimal('total', 10, 2);
            $table->json('address'); // Delivery address
            $table->json('author'); // Customer details
            $table->json('vendor'); // Restaurant details
            $table->json('driver')->nullable(); // Driver details
            $table->string('paymentMethod');
            $table->boolean('paymentStatus')->default(false);
            $table->string('orderType')->default('delivery'); // delivery, pickup, dine_in
            $table->text('notes')->nullable();
            $table->timestamp('estimatedTimeToPrepare')->nullable();
            $table->timestamps();

            $table->index(['authorID', 'status']);
            $table->index(['vendorID', 'status']);
            $table->index(['driverID', 'status']);
            $table->index(['status', 'createdAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
