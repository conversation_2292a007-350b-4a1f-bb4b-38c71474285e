<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('type'); // order, general, promotion
            $table->string('title');
            $table->text('message');
            $table->string('recipientType'); // customer, vendor, driver, all
            $table->string('recipientId')->nullable(); // specific user ID
            $table->json('data')->nullable(); // additional data
            $table->boolean('sent')->default(false);
            $table->timestamp('sentAt')->nullable();
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['recipientType', 'recipientId']);
            $table->index(['type', 'sent']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
