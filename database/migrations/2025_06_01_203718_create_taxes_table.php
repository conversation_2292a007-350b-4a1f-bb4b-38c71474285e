<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('taxes', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('type'); // percentage, amount
            $table->decimal('tax', 10, 2);
            $table->boolean('enable')->default(true);
            $table->string('country')->nullable();
            $table->timestamps();

            $table->index(['enable', 'country']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('taxes');
    }
};
