<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drivers', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('firstName');
            $table->string('lastName');
            $table->string('email')->unique();
            $table->string('phoneNumber');
            $table->string('countryCode')->nullable();
            $table->text('profilePictureURL')->nullable();
            $table->string('role')->default('driver');
            $table->boolean('active')->default(true);
            $table->boolean('isDocumentVerify')->default(false);
            $table->string('vehicleType')->nullable(); // car, bike, bicycle
            $table->string('vehicleNumber')->nullable();
            $table->text('carName')->nullable();
            $table->text('carNumber')->nullable();
            $table->text('carPictureURL')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->boolean('isOnline')->default(false);
            $table->decimal('wallet_amount', 10, 2)->default(0);
            $table->string('zoneId')->nullable();
            $table->string('fcmToken')->nullable();
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['active', 'isOnline']);
            $table->index(['zoneId', 'active']);
            $table->index(['email']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drivers');
    }
};
