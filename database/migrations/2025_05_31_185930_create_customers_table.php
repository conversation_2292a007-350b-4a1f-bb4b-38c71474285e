<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase UID
            $table->string('firstName')->nullable();
            $table->string('lastName')->nullable();
            $table->string('email')->nullable();
            $table->string('phoneNumber')->nullable();
            $table->string('countryCode')->nullable();
            $table->string('profilePictureURL')->nullable();
            $table->string('role')->default('customer');
            $table->boolean('active')->default(true);
            $table->boolean('isDocumentVerify')->default(false);
            $table->boolean('isActive')->nullable();
            $table->string('provider')->nullable();
            $table->string('appIdentifier')->nullable();
            $table->string('zoneId')->nullable();
            $table->string('fcmToken')->nullable();
            $table->decimal('wallet_amount', 10, 2)->default(0);
            $table->json('shippingAddress')->nullable();
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
