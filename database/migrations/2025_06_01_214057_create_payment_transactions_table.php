<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('payment_transactions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('orderID'); // Related order
            $table->string('userID'); // Customer ID
            $table->string('vendorID')->nullable(); // Restaurant ID
            $table->string('driverID')->nullable(); // Driver ID
            $table->string('paymentMethodID'); // Payment method used
            $table->string('transactionType'); // payment, refund, partial_refund
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->string('status'); // pending, completed, failed, cancelled
            $table->string('gatewayTransactionID')->nullable(); // External transaction ID
            $table->json('gatewayResponse')->nullable(); // Full gateway response
            $table->decimal('processingFee', 10, 2)->default(0);
            $table->text('failureReason')->nullable();
            $table->timestamp('processedAt')->nullable();
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['orderID', 'status']);
            $table->index(['userID', 'transactionType']);
            $table->index(['vendorID', 'status']);
            $table->index(['status', 'processedAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
