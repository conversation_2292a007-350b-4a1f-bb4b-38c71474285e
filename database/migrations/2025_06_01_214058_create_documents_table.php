<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('documents', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('title'); // Document name
            $table->text('description')->nullable();
            $table->string('type'); // driver_license, vehicle_registration, insurance, etc.
            $table->boolean('isRequired')->default(false); // Required for verification
            $table->boolean('isEnabled')->default(true);
            $table->json('allowedFormats')->nullable(); // Allowed file formats
            $table->integer('maxFileSize')->default(5120); // Max file size in KB
            $table->integer('order')->default(0); // Display order
            $table->timestamps();

            $table->index(['type', 'isEnabled']);
            $table->index(['isRequired', 'isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
