<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mysql')->create('withdraw_methods', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name'); // Bank Transfer, PayPal, Stripe, etc.
            $table->string('type'); // bank_transfer, paypal, stripe, etc.
            $table->text('description')->nullable();
            $table->text('image')->nullable(); // Method logo
            $table->boolean('isEnabled')->default(true);
            $table->json('requiredFields')->nullable(); // Required fields for this method
            $table->decimal('minimumAmount', 10, 2)->default(0); // Minimum withdrawal amount
            $table->decimal('maximumAmount', 10, 2)->nullable(); // Maximum withdrawal amount
            $table->decimal('processingFee', 5, 2)->default(0); // Processing fee percentage
            $table->decimal('fixedFee', 10, 2)->default(0); // Fixed fee amount
            $table->integer('processingDays')->default(1); // Processing time in days
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->integer('order')->default(0); // Display order
            $table->timestamps();

            $table->index(['isEnabled', 'order']);
            $table->index(['type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('withdraw_methods');
    }
};
