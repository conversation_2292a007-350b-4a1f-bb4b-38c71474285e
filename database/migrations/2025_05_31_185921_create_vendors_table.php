<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase UID
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('phonenumber')->nullable();
            $table->integer('reviewsCount')->default(0);
            $table->string('categoryPhoto')->nullable();
            $table->boolean('enabledDiveInFuture')->default(true);
            $table->json('photos')->nullable();
            $table->string('restaurantCost')->nullable();
            $table->string('zoneId')->nullable();
            $table->string('fcmToken')->nullable();
            $table->json('workingHours')->nullable();
            $table->string('categoryID')->nullable();
            $table->json('DeliveryCharge')->nullable();
            $table->json('restaurantMenuPhotos')->nullable();
            $table->string('author')->nullable();
            $table->string('geohash')->nullable();
            $table->string('categoryTitle')->nullable();
            $table->json('coordinates')->nullable();
            $table->string('photo')->nullable();
            $table->json('filters')->nullable();
            $table->string('closeDineTime')->nullable();
            $table->decimal('walletAmount', 10, 2)->nullable();
            $table->string('authorProfilePic')->nullable();
            $table->string('openDineTime')->nullable();
            $table->string('authorName')->nullable();
            $table->decimal('reviewsSum', 8, 2)->default(0);
            $table->string('location')->nullable();
            $table->json('specialDiscount')->nullable();
            $table->boolean('hidephotos')->nullable();
            $table->boolean('reststatus')->nullable();
            $table->boolean('dine_in_active')->nullable();
            $table->boolean('specialDiscountEnable')->default(false);
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
};
