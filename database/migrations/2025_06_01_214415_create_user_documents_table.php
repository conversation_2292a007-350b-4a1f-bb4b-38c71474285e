<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('user_documents', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('userID'); // Driver or Vendor ID
            $table->string('userType'); // driver, vendor
            $table->string('documentID'); // Reference to documents table
            $table->text('frontImage'); // Front side of document
            $table->text('backImage')->nullable(); // Back side of document
            $table->string('status')->default('pending'); // pending, approved, rejected
            $table->text('rejectionReason')->nullable();
            $table->string('verifiedBy')->nullable(); // Admin who verified
            $table->timestamp('verifiedAt')->nullable();
            $table->timestamp('expiryDate')->nullable(); // Document expiry
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['userID', 'userType']);
            $table->index(['documentID', 'status']);
            $table->index(['status', 'verifiedAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_documents');
    }
};
