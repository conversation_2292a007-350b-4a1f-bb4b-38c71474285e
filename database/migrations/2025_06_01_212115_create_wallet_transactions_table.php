<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('wallet_transactions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('userID'); // Customer, Vendor, or Driver ID
            $table->string('userType'); // customer, vendor, driver
            $table->string('type'); // credit, debit
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_before', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->string('reason'); // order_refund, order_payment, withdrawal, etc.
            $table->string('orderID')->nullable(); // Related order if applicable
            $table->text('note')->nullable();
            $table->string('status')->default('completed'); // pending, completed, failed
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['userID', 'userType']);
            $table->index(['type', 'status']);
            $table->index(['orderID']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
