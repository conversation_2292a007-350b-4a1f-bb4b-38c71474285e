<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gift_cards', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('title');
            $table->text('description')->nullable();
            $table->text('image')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('code')->unique();
            $table->boolean('isEnabled')->default(true);
            $table->timestamp('expiresAt')->nullable();
            $table->string('giftBy')->nullable(); // User who purchased
            $table->string('giftTo')->nullable(); // User who received
            $table->boolean('isUsed')->default(false);
            $table->timestamp('usedAt')->nullable();
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['code', 'isEnabled']);
            $table->index(['isUsed', 'expiresAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gift_cards');
    }
};
