<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mysql')->create('payment_methods', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name'); // Stripe, PayPal, Razorpay, etc.
            $table->string('type'); // card, wallet, bank_transfer, cash
            $table->text('description')->nullable();
            $table->text('image')->nullable(); // Payment method logo
            $table->boolean('isEnabled')->default(true);
            $table->json('settings')->nullable(); // API keys, configurations
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->decimal('processingFee', 5, 2)->default(0); // Processing fee percentage
            $table->decimal('fixedFee', 10, 2)->default(0); // Fixed fee amount
            $table->integer('order')->default(0); // Display order
            $table->boolean('isLive')->default(false); // Live/Sandbox mode
            $table->timestamps();

            $table->index(['isEnabled', 'order']);
            $table->index(['type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
