<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('title');
            $table->text('description')->nullable();
            $table->text('image')->nullable();
            $table->boolean('publish')->default(true);
            $table->integer('order')->default(0);
            $table->json('filters')->nullable();
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['publish', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
