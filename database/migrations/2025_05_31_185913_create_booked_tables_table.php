<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('booked_tables', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase document ID
            $table->timestamp('date')->nullable();
            $table->string('occasion')->nullable();
            $table->string('guestLastName')->nullable();
            $table->string('guestFirstName')->nullable();
            $table->string('guestEmail')->nullable();
            $table->string('guestPhone')->nullable();
            $table->string('discount')->default('0');
            $table->string('vendorID')->nullable();
            $table->string('authorID')->nullable();
            $table->string('totalGuest')->nullable();
            $table->string('specialRequest')->nullable();
            $table->string('discountType')->nullable();
            $table->boolean('firstVisit')->default(false);
            $table->string('status')->nullable();
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('booked_tables');
    }
};
