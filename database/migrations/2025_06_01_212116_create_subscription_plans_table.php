<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('subscription_plans', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('image')->nullable();
            $table->decimal('price', 10, 2);
            $table->string('type'); // free, paid
            $table->integer('expiryDay'); // -1 for unlimited
            $table->integer('itemLimit'); // -1 for unlimited
            $table->integer('orderLimit'); // -1 for unlimited
            $table->json('plan_points')->nullable(); // Features list
            $table->json('features')->nullable(); // Feature flags
            $table->boolean('isEnable')->default(true);
            $table->integer('place')->default(0); // Sort order
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['type', 'isEnable']);
            $table->index(['price']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
