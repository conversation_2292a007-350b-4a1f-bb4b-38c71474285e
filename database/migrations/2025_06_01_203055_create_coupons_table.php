<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('code')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->text('image')->nullable();
            $table->string('discountType'); // percentage, amount
            $table->decimal('discount', 10, 2);
            $table->decimal('minimumAmount', 10, 2)->default(0);
            $table->decimal('maximumAmount', 10, 2)->nullable();
            $table->boolean('enabled')->default(true);
            $table->timestamp('expiresAt')->nullable();
            $table->string('vendorID')->nullable(); // null for global coupons
            $table->integer('usageLimit')->nullable();
            $table->integer('usageCount')->default(0);
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['code', 'enabled']);
            $table->index(['vendorID', 'enabled']);
            $table->index(['enabled', 'expiresAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
