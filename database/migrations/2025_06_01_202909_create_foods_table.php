<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('foods', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->decimal('disPrice', 10, 2)->nullable();
            $table->text('photo')->nullable();
            $table->json('photos')->nullable();
            $table->string('categoryID');
            $table->string('vendorID');
            $table->boolean('publish')->default(true);
            $table->boolean('nonveg')->default(false);
            $table->json('addOnsTitle')->nullable();
            $table->json('addOns')->nullable();
            $table->json('size')->nullable();
            $table->integer('calories')->nullable();
            $table->text('ingredients')->nullable();
            $table->json('nutritions')->nullable();
            $table->boolean('takeawayOption')->default(true);
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['categoryID', 'vendorID', 'publish']);
            $table->index(['vendorID', 'publish']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('foods');
    }
};
