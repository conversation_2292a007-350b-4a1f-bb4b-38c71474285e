<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Set the database connection to firebase (PostgreSQL)
        Schema::connection('firebase')->table('foods', function (Blueprint $table) {
            $table->foreign('categoryID')->references('id')->on('categories')->onDelete('cascade');
            $table->foreign('vendorID')->references('id')->on('vendors')->onDelete('cascade');
        });

        Schema::connection('firebase')->table('orders', function (Blueprint $table) {
            $table->foreign('authorID')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('vendorID')->references('id')->on('vendors')->onDelete('cascade');
            $table->foreign('driverID')->references('id')->on('drivers')->onDelete('set null');
        });

        Schema::connection('firebase')->table('booked_tables', function (Blueprint $table) {
            $table->foreign('authorID')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('vendorID')->references('id')->on('vendors')->onDelete('cascade');
        });

        // Skip coupons foreign key for now as vendorID can be null
        // Schema::connection('firebase')->table('coupons', function (Blueprint $table) {
        //     $table->foreign('vendorID')->references('id')->on('vendors')->onDelete('cascade');
        // });

        // Skip zone foreign keys for now as many records don't have zoneId set
        // Schema::connection('firebase')->table('drivers', function (Blueprint $table) {
        //     $table->foreign('zoneId')->references('id')->on('zones')->onDelete('set null');
        // });

        // Schema::connection('firebase')->table('vendors', function (Blueprint $table) {
        //     $table->foreign('zoneId')->references('id')->on('zones')->onDelete('set null');
        // });

        // Schema::connection('firebase')->table('customers', function (Blueprint $table) {
        //     $table->foreign('zoneId')->references('id')->on('zones')->onDelete('set null');
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->table('foods', function (Blueprint $table) {
            $table->dropForeign(['categoryID']);
            $table->dropForeign(['vendorID']);
        });

        Schema::connection('firebase')->table('orders', function (Blueprint $table) {
            $table->dropForeign(['authorID']);
            $table->dropForeign(['vendorID']);
            $table->dropForeign(['driverID']);
        });

        Schema::connection('firebase')->table('booked_tables', function (Blueprint $table) {
            $table->dropForeign(['authorID']);
            $table->dropForeign(['vendorID']);
        });

        Schema::connection('firebase')->table('coupons', function (Blueprint $table) {
            $table->dropForeign(['vendorID']);
        });

        Schema::connection('firebase')->table('drivers', function (Blueprint $table) {
            $table->dropForeign(['zoneId']);
        });

        Schema::connection('firebase')->table('vendors', function (Blueprint $table) {
            $table->dropForeign(['zoneId']);
        });

        Schema::connection('firebase')->table('customers', function (Blueprint $table) {
            $table->dropForeign(['zoneId']);
        });
    }
};
