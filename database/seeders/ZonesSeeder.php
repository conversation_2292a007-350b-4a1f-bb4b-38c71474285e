<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Zone;

class ZonesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $zones = [
            [
                'id' => 'wjmdEiZwPmoqqRvcr7b7', // This ID exists in the vendor data
                'name' => 'Central Zone',
                'description' => 'Main delivery zone covering central areas',
                'coordinates' => [
                    ['lat' => 23.0225, 'lng' => 72.5714],
                    ['lat' => 23.0225, 'lng' => 72.6714],
                    ['lat' => 22.9225, 'lng' => 72.6714],
                    ['lat' => 22.9225, 'lng' => 72.5714]
                ],
                'active' => true,
                'deliveryCharge' => 25.00,
            ],
            [
                'id' => 'zone_north',
                'name' => 'North Zone',
                'description' => 'Northern delivery zone',
                'coordinates' => [
                    ['lat' => 23.1225, 'lng' => 72.5714],
                    ['lat' => 23.1225, 'lng' => 72.6714],
                    ['lat' => 23.0225, 'lng' => 72.6714],
                    ['lat' => 23.0225, 'lng' => 72.5714]
                ],
                'active' => true,
                'deliveryCharge' => 30.00,
            ],
            [
                'id' => 'zone_south',
                'name' => 'South Zone',
                'description' => 'Southern delivery zone',
                'coordinates' => [
                    ['lat' => 22.9225, 'lng' => 72.5714],
                    ['lat' => 22.9225, 'lng' => 72.6714],
                    ['lat' => 22.8225, 'lng' => 72.6714],
                    ['lat' => 22.8225, 'lng' => 72.5714]
                ],
                'active' => true,
                'deliveryCharge' => 35.00,
            ]
        ];

        foreach ($zones as $zone) {
            Zone::updateOrCreate(
                ['id' => $zone['id']],
                $zone
            );
        }

        $this->command->info('Zones seeded successfully!');
    }
}
