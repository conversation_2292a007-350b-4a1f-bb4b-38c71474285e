<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use Carbon\Carbon;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'id' => 'cat_pizza',
                'title' => 'Pizza',
                'description' => 'Delicious pizzas with various toppings',
                'image' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
                'publish' => true,
                'order' => 1,
                'filters' => ['vegetarian', 'non-vegetarian'],
                'createdAt' => Carbon::now(),
            ],
            [
                'id' => 'cat_burgers',
                'title' => 'Burgers',
                'description' => 'Juicy burgers with fresh ingredients',
                'image' => 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400',
                'publish' => true,
                'order' => 2,
                'filters' => ['beef', 'chicken', 'vegetarian'],
                'createdAt' => Carbon::now(),
            ],
            [
                'id' => 'cat_pasta',
                'title' => 'Pasta',
                'description' => 'Italian pasta dishes',
                'image' => 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=400',
                'publish' => true,
                'order' => 3,
                'filters' => ['vegetarian', 'seafood'],
                'createdAt' => Carbon::now(),
            ],
            [
                'id' => 'cat_desserts',
                'title' => 'Desserts',
                'description' => 'Sweet treats and desserts',
                'image' => 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400',
                'publish' => true,
                'order' => 4,
                'filters' => ['sweet', 'chocolate', 'fruit'],
                'createdAt' => Carbon::now(),
            ],
            [
                'id' => 'cat_beverages',
                'title' => 'Beverages',
                'description' => 'Refreshing drinks and beverages',
                'image' => 'https://images.unsplash.com/photo-**********-f90425340c7e?w=400',
                'publish' => true,
                'order' => 5,
                'filters' => ['cold', 'hot', 'alcoholic', 'non-alcoholic'],
                'createdAt' => Carbon::now(),
            ],
            [
                'id' => 'cat_salads',
                'title' => 'Salads',
                'description' => 'Fresh and healthy salads',
                'image' => 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',
                'publish' => true,
                'order' => 6,
                'filters' => ['healthy', 'vegetarian', 'vegan'],
                'createdAt' => Carbon::now(),
            ],
        ];

        foreach ($categories as $category) {
            Category::updateOrCreate(
                ['id' => $category['id']],
                $category
            );
        }

        $this->command->info('Categories seeded successfully!');
    }
}
