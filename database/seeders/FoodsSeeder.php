<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Food;
use App\Models\Vendor;
use Carbon\Carbon;

class FoodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing vendor IDs
        $vendorIds = Vendor::pluck('id')->toArray();

        if (empty($vendorIds)) {
            $this->command->error('No vendors found. Please seed vendors first.');
            return;
        }

        $foods = [
            // Pizza Category
            [
                'id' => 'food_margherita',
                'name' => 'Margherita Pizza',
                'description' => 'Classic pizza with tomato sauce, mozzarella, and fresh basil',
                'price' => 12.99,
                'disPrice' => 10.99,
                'photo' => 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=400',
                'photos' => ['https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=400'],
                'categoryID' => 'cat_pizza',
                'vendorID' => $vendorIds[0],
                'publish' => true,
                'nonveg' => false,
                'addOnsTitle' => ['Extra Cheese', 'Olives'],
                'addOns' => [
                    ['name' => 'Extra Cheese', 'price' => 2.00],
                    ['name' => 'Olives', 'price' => 1.50]
                ],
                'size' => [
                    ['name' => 'Small', 'price' => 12.99],
                    ['name' => 'Medium', 'price' => 16.99],
                    ['name' => 'Large', 'price' => 20.99]
                ],
                'calories' => 250,
                'ingredients' => 'Tomato sauce, mozzarella cheese, fresh basil, olive oil',
                'nutritions' => ['protein' => '12g', 'carbs' => '30g', 'fat' => '10g'],
                'takeawayOption' => true,
                'createdAt' => Carbon::now(),
            ],
            [
                'id' => 'food_pepperoni',
                'name' => 'Pepperoni Pizza',
                'description' => 'Classic pepperoni pizza with mozzarella cheese',
                'price' => 15.99,
                'disPrice' => null,
                'photo' => 'https://images.unsplash.com/photo-1628840042765-356cda07504e?w=400',
                'photos' => ['https://images.unsplash.com/photo-1628840042765-356cda07504e?w=400'],
                'categoryID' => 'cat_pizza',
                'vendorID' => $vendorIds[0],
                'publish' => true,
                'nonveg' => true,
                'addOnsTitle' => ['Extra Pepperoni', 'Mushrooms'],
                'addOns' => [
                    ['name' => 'Extra Pepperoni', 'price' => 3.00],
                    ['name' => 'Mushrooms', 'price' => 2.00]
                ],
                'size' => [
                    ['name' => 'Small', 'price' => 15.99],
                    ['name' => 'Medium', 'price' => 19.99],
                    ['name' => 'Large', 'price' => 23.99]
                ],
                'calories' => 320,
                'ingredients' => 'Tomato sauce, mozzarella cheese, pepperoni',
                'nutritions' => ['protein' => '15g', 'carbs' => '32g', 'fat' => '14g'],
                'takeawayOption' => true,
                'createdAt' => Carbon::now(),
            ]
        ];

        // Add more foods for different categories
        $this->addBurgerFoods($foods, $vendorIds);
        $this->addPastaFoods($foods, $vendorIds);
        $this->addBeverageFoods($foods, $vendorIds);

        foreach ($foods as $food) {
            Food::updateOrCreate(
                ['id' => $food['id']],
                $food
            );
        }

        $this->command->info('Foods seeded successfully! Total: ' . count($foods) . ' items');
    }

    private function addBurgerFoods(&$foods, $vendorIds)
    {
        $burgerFoods = [
            [
                'id' => 'food_classic_burger',
                'name' => 'Classic Beef Burger',
                'description' => 'Juicy beef patty with lettuce, tomato, and special sauce',
                'price' => 9.99,
                'disPrice' => null,
                'photo' => 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400',
                'photos' => ['https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400'],
                'categoryID' => 'cat_burgers',
                'vendorID' => $vendorIds[1] ?? $vendorIds[0],
                'publish' => true,
                'nonveg' => true,
                'addOnsTitle' => ['Extra Cheese', 'Bacon'],
                'addOns' => [
                    ['name' => 'Extra Cheese', 'price' => 1.50],
                    ['name' => 'Bacon', 'price' => 2.50]
                ],
                'size' => [
                    ['name' => 'Regular', 'price' => 9.99],
                    ['name' => 'Large', 'price' => 12.99]
                ],
                'calories' => 450,
                'ingredients' => 'Beef patty, lettuce, tomato, onion, pickles, special sauce',
                'nutritions' => ['protein' => '25g', 'carbs' => '35g', 'fat' => '20g'],
                'takeawayOption' => true,
                'createdAt' => Carbon::now(),
            ]
        ];

        $foods = array_merge($foods, $burgerFoods);
    }

    private function addPastaFoods(&$foods, $vendorIds)
    {
        $pastaFoods = [
            [
                'id' => 'food_spaghetti_carbonara',
                'name' => 'Spaghetti Carbonara',
                'description' => 'Classic Italian pasta with eggs, cheese, and pancetta',
                'price' => 14.99,
                'disPrice' => null,
                'photo' => 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=400',
                'photos' => ['https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=400'],
                'categoryID' => 'cat_pasta',
                'vendorID' => $vendorIds[2] ?? $vendorIds[0],
                'publish' => true,
                'nonveg' => true,
                'addOnsTitle' => ['Extra Parmesan', 'Garlic Bread'],
                'addOns' => [
                    ['name' => 'Extra Parmesan', 'price' => 2.00],
                    ['name' => 'Garlic Bread', 'price' => 3.50]
                ],
                'size' => [
                    ['name' => 'Regular', 'price' => 14.99],
                    ['name' => 'Large', 'price' => 18.99]
                ],
                'calories' => 380,
                'ingredients' => 'Spaghetti, eggs, parmesan cheese, pancetta, black pepper',
                'nutritions' => ['protein' => '18g', 'carbs' => '45g', 'fat' => '15g'],
                'takeawayOption' => true,
                'createdAt' => Carbon::now(),
            ]
        ];

        $foods = array_merge($foods, $pastaFoods);
    }

    private function addBeverageFoods(&$foods, $vendorIds)
    {
        $beverageFoods = [
            [
                'id' => 'food_coca_cola',
                'name' => 'Coca Cola',
                'description' => 'Classic refreshing cola drink',
                'price' => 2.99,
                'disPrice' => null,
                'photo' => 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',
                'photos' => ['https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400'],
                'categoryID' => 'cat_beverages',
                'vendorID' => $vendorIds[0],
                'publish' => true,
                'nonveg' => false,
                'addOnsTitle' => [],
                'addOns' => [],
                'size' => [
                    ['name' => 'Small (330ml)', 'price' => 2.99],
                    ['name' => 'Large (500ml)', 'price' => 3.99]
                ],
                'calories' => 140,
                'ingredients' => 'Carbonated water, sugar, caffeine, natural flavors',
                'nutritions' => ['protein' => '0g', 'carbs' => '39g', 'fat' => '0g'],
                'takeawayOption' => true,
                'createdAt' => Carbon::now(),
            ]
        ];

        $foods = array_merge($foods, $beverageFoods);
    }
}
